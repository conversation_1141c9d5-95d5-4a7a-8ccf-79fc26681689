<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOC to HTML Converter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.4.2/mammoth.browser.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 700;
        }

        .upload-section {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-section:hover {
            border-color: #764ba2;
            background: rgba(118, 75, 162, 0.1);
        }

        .upload-section.dragover {
            border-color: #48bb78;
            background: rgba(72, 187, 120, 0.1);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4em;
            margin-bottom: 20px;
            color: #667eea;
        }

        .upload-text {
            font-size: 1.2em;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #718096;
            font-size: 0.9em;
        }

        #fileInput {
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .output-section {
            margin-top: 30px;
            display: none;
        }

        .output-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 1em;
            font-weight: 600;
            color: #718096;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab:hover {
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .preview-container {
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            background: white;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .code-container {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 500px;
            overflow-y: auto;
            margin-bottom: 20px;
        }

        .status {
            padding: 10px 20px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }

        .status.success {
            background: #c6f6d5;
            color: #2d7d32;
            border: 1px solid #68d391;
        }

        .status.error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #fc8181;
        }

        .status.info {
            background: #bee3f8;
            color: #2b6cb0;
            border: 1px solid #63b3ed;
        }

        .copy-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .copy-btn:hover {
            background: #38a169;
        }

        .download-btn {
            background: #ed8936;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            margin-bottom: 10px;
            margin-left: 10px;
        }

        .download-btn:hover {
            background: #dd6b20;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            h1 {
                font-size: 2em;
            }

            .upload-section {
                padding: 20px;
            }

            .upload-icon {
                font-size: 3em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 DOC to HTML Converter</h1>
        
        <div class="upload-section" id="uploadSection">
            <div class="upload-icon">📄</div>
            <div class="upload-text">Drop your DOC file here or click to browse</div>
            <div class="upload-subtext">Supports .doc and .docx files up to 10MB</div>
            <input type="file" id="fileInput" accept=".doc,.docx" />
        </div>

        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div id="status"></div>

        <div class="output-section" id="outputSection">
            <div class="output-tabs">
                <button class="tab active" data-tab="preview">Preview</button>
                <button class="tab" data-tab="html">HTML Code</button>
            </div>

            <div class="tab-content active" id="preview">
                <button class="copy-btn" onclick="copyPreview()">Copy Preview</button>
                <button class="download-btn" onclick="downloadHTML()">Download HTML</button>
                <div class="preview-container" id="previewContainer"></div>
            </div>

            <div class="tab-content" id="html">
                <button class="copy-btn" onclick="copyHTML()">Copy HTML</button>
                <button class="download-btn" onclick="downloadHTML()">Download HTML</button>
                <div class="code-container" id="htmlContainer"></div>
            </div>
        </div>
    </div>

    <script>
        let convertedHTML = '';
        let originalFileName = '';

        // DOM elements
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const status = document.getElementById('status');
        const outputSection = document.getElementById('outputSection');
        const previewContainer = document.getElementById('previewContainer');
        const htmlContainer = document.getElementById('htmlContainer');

        // Event listeners
        uploadSection.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFile);

        // Drag and drop
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile({ target: { files } });
            }
        });

        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                switchTab(tabName);
            });
        });

        function switchTab(tabName) {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            originalFileName = file.name.replace(/\.[^/.]+$/, "");

            // Validate file type
            if (!file.name.match(/\.(doc|docx)$/i)) {
                showStatus('Please select a valid DOC or DOCX file.', 'error');
                return;
            }

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                showStatus('File size must be less than 10MB.', 'error');
                return;
            }

            convertFile(file);
        }

        function convertFile(file) {
            showStatus('Converting file...', 'info');
            showProgress(0);

            const reader = new FileReader();
            reader.onload = function(e) {
                showProgress(50);
                
                mammoth.convertToHtml({ arrayBuffer: e.target.result })
                    .then(function(result) {
                        convertedHTML = result.value;
                        showProgress(100);
                        
                        // Display results
                        previewContainer.innerHTML = convertedHTML;
                        htmlContainer.textContent = convertedHTML;
                        
                        outputSection.style.display = 'block';
                        
                        let statusMsg = 'Conversion completed successfully!';
                        if (result.messages.length > 0) {
                            statusMsg += ` (${result.messages.length} warnings)`;
                        }
                        showStatus(statusMsg, 'success');
                        
                        hideProgress();
                    })
                    .catch(function(error) {
                        console.error('Conversion error:', error);
                        showStatus('Error converting file: ' + error.message, 'error');
                        hideProgress();
                    });
            };

            reader.onerror = function() {
                showStatus('Error reading file.', 'error');
                hideProgress();
            };

            reader.readAsArrayBuffer(file);
        }

        function showStatus(message, type) {
            status.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showProgress(percent) {
            progressBar.style.display = 'block';
            progressFill.style.width = percent + '%';
        }

        function hideProgress() {
            setTimeout(() => {
                progressBar.style.display = 'none';
                progressFill.style.width = '0%';
            }, 500);
        }

        function copyHTML() {
            navigator.clipboard.writeText(convertedHTML).then(() => {
                showStatus('HTML code copied to clipboard!', 'success');
            }).catch(() => {
                showStatus('Failed to copy HTML code.', 'error');
            });
        }

        function copyPreview() {
            navigator.clipboard.writeText(previewContainer.innerHTML).then(() => {
                showStatus('Preview HTML copied to clipboard!', 'success');
            }).catch(() => {
                showStatus('Failed to copy preview.', 'error');
            });
        }

        function downloadHTML() {
            const blob = new Blob([convertedHTML], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = (originalFileName || 'converted') + '.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            showStatus('HTML file downloaded!', 'success');
        }
    </script>
</body>
</html>